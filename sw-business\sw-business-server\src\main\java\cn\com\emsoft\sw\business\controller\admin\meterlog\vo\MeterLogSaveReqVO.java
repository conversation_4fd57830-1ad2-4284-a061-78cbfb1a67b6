package cn.com.emsoft.sw.business.controller.admin.meterlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 表务日志新增/修改 Request VO")
@Data
public class MeterLogSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "30946")
    private Long id;

    @Schema(description = "水表id，关联水表信息", example = "26430")
    private Long meterId;

    @Schema(description = "水表出入库id，为出入库时有值", example = "5699")
    private Long meterInOutId;

    @Schema(description = "日志类型，关联日志类型", example = "1")
    private Short type;

    @Schema(description = "操作结果，成功/失败")
    private String result;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "状态：0-否，1-是", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态：0-否，1-是不能为空")
    private Short status;

}