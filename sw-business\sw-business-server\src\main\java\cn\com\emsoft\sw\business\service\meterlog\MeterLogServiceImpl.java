package cn.com.emsoft.sw.business.service.meterlog;

import cn.com.emsoft.sw.business.controller.admin.meterlog.vo.MeterLogPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meterlog.vo.MeterLogSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterlog.MeterLogDO;
import cn.com.emsoft.sw.business.dal.mysql.meterlog.MeterLogMapper;
import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;

import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.*;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.com.emsoft.sw.framework.common.util.collection.CollectionUtils.convertList;
import static cn.com.emsoft.sw.framework.common.util.collection.CollectionUtils.diffList;


/**
 * 表务日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MeterLogServiceImpl implements MeterLogService {

    @Resource
    private MeterLogMapper meterLogMapper;

    @Override
    public Long createMeterLog(MeterLogSaveReqVO createReqVO) {
        // 插入
        MeterLogDO meterLog = BeanUtils.toBean(createReqVO, MeterLogDO.class);
        meterLogMapper.insert(meterLog);
        // 返回
        return meterLog.getId();
    }

    @Override
    public void updateMeterLog(MeterLogSaveReqVO updateReqVO) {
        // 校验存在
        validateMeterLogExists(updateReqVO.getId());
        // 更新
        MeterLogDO updateObj = BeanUtils.toBean(updateReqVO, MeterLogDO.class);
        meterLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteMeterLog(Long id) {
        // 校验存在
        validateMeterLogExists(id);
        // 删除
        meterLogMapper.deleteById(id);
    }

    @Override
        public void deleteMeterLogListByIds(List<Long> ids) {
        // 校验存在
        validateMeterLogExists(ids);
        // 删除
        meterLogMapper.deleteByIds(ids);
        }

    private void validateMeterLogExists(List<Long> ids) {
        List<MeterLogDO> list = meterLogMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(METER_LOG_NOT_EXISTS);
        }
    }

    private void validateMeterLogExists(Long id) {
        if (meterLogMapper.selectById(id) == null) {
            throw exception(METER_LOG_NOT_EXISTS);
        }
    }

    @Override
    public MeterLogDO getMeterLog(Long id) {
        return meterLogMapper.selectById(id);
    }

    @Override
    public PageResult<MeterLogDO> getMeterLogPage(MeterLogPageReqVO pageReqVO) {
        return meterLogMapper.selectPage(pageReqVO);
    }

}