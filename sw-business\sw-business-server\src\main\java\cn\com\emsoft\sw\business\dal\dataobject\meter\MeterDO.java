package cn.com.emsoft.sw.business.dal.dataobject.meter;

import cn.com.emsoft.sw.framework.common.validation.GetterRef;
import cn.com.emsoft.sw.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.com.emsoft.sw.framework.mybatis.core.dataobject.BaseDO;

/**
 * 水表信息 DO
 *
 * <AUTHOR>
 */
@TableName("biz_meter")
@KeySequence("biz_meter_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@GetterRef
public class MeterDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 入库单号，关联水表出入库单
     */
    private Long inMeter;
    /**
     * 出库单号，关联水表出入库单
     */
    private Long outMeter;
    /**
     * 水表厂家代码，关联水表厂家表
     */
    private String makerCode;
    /**
     * 水表型号代码，关联水表型号表
     */
    private String modelCode;
    /**
     * 水表口径代码，关联水表口径表
     */
    private Long caliberCode;
    /**
     * 水表量程代码，关联水表量程表
     */
    private Long rangeCode;
    /**
     * 水表分类/类型
     *
     * 枚举 {@link TODO meter_oth_type 对应的类}
     */
    private Short type;
    /**
     * 钢印号
     */
    private String steelMark;
    /**
     * 水表编号
     */
    private String sealNumber;
    /**
     * 条形码
     */
    private String barCode;
    /**
     * 强检编号
     */
    private String checkCode;
    /**
     * 生产日期
     */
    private LocalDateTime makeDate;
    /**
     * 强检日期
     */
    private LocalDateTime checkDate;
    /**
     * 采集号
     */
    private String collectCode;
    /**
     * GPSX坐标
     */
    private BigDecimal gpsx;
    /**
     * GPSY坐标
     */
    private BigDecimal gpsy;
    /**
     * 纬度
     */
    private BigDecimal latitude;
    /**
     * 经度
     */
    private BigDecimal longitude;
    /**
     * 高度，单位：米
     */
    private BigDecimal highly;
    /**
     * 设备识别码
     */
    private String imei;
    /**
     * IMSI
     */
    private String imsi;
    /**
     * 模块号
     */
    private String moduleCode;
    /**
     * NFC编号
     */
    private String nfcCode;
    /**
     * 二维码编号
     */
    private String qrCode;
    /**
     * 计量编号
     */
    private String measureNo;
    /**
     * 水表状态
     */
    private Short meterStatus;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态：0-否，1-是
     */
    private Short status;


}