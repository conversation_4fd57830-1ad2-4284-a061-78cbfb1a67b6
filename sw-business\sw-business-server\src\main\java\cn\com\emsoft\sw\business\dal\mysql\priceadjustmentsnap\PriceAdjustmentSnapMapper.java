package cn.com.emsoft.sw.business.dal.mysql.priceadjustmentsnap;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.priceadjustmentsnap.vo.PriceAdjustmentSnapPageReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.priceadjustmentsnap.PriceAdjustmentSnapDO;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;

import org.apache.ibatis.annotations.Mapper;

/**
 * 水价调整快照（历史） Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PriceAdjustmentSnapMapper extends BaseMapperX<PriceAdjustmentSnapDO> {

    default PageResult<PriceAdjustmentSnapDO> selectPage(PriceAdjustmentSnapPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PriceAdjustmentSnapDO>()
                .orderByDesc(PriceAdjustmentSnapDO::getId));
    }

    /**
     * 查询最大的快照版本号
     */
    default String selectMaxSnapCode() {
        PriceAdjustmentSnapDO maxSnap = selectOne(new LambdaQueryWrapperX<PriceAdjustmentSnapDO>()
                .orderByDesc(PriceAdjustmentSnapDO::getId)
                .last("LIMIT 1"));
        return maxSnap != null ? maxSnap.getCode() : null;
    }

    /**
     * 根据快照代码查询快照信息
     */
    default PriceAdjustmentSnapDO selectByCode(String code) {
        return selectOne(PriceAdjustmentSnapDO::getCode, code);
    }

}