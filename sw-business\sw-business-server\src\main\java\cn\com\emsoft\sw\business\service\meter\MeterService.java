package cn.com.emsoft.sw.business.service.meter;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.meter.vo.MeterPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meter.vo.MeterSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meter.MeterDO;
import jakarta.validation.*;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;

/**
 * 水表信息 Service 接口
 *
 * <AUTHOR>
 */
public interface MeterService {

    /**
     * 创建水表信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMeter(@Valid MeterSaveReqVO createReqVO);

    /**
     * 更新水表信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMeter(@Valid MeterSaveReqVO updateReqVO);

    /**
     * 删除水表信息
     *
     * @param id 编号
     */
    void deleteMeter(Long id);

    /**
    * 批量删除水表信息
    *
    * @param ids 编号
    */
    void deleteMeterListByIds(List<Long> ids);

    /**
     * 获得水表信息
     *
     * @param id 编号
     * @return 水表信息
     */
    MeterDO getMeter(Long id);

    /**
     * 获得水表信息分页
     *
     * @param pageReqVO 分页查询
     * @return 水表信息分页
     */
    PageResult<MeterDO> getMeterPage(MeterPageReqVO pageReqVO);

}