package cn.com.emsoft.sw.business.controller.admin.meterlog;

import cn.com.emsoft.sw.business.controller.admin.meterlog.vo.MeterLogPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meterlog.vo.MeterLogRespVO;
import cn.com.emsoft.sw.business.controller.admin.meterlog.vo.MeterLogSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterlog.MeterLogDO;
import cn.com.emsoft.sw.business.service.meterlog.MeterLogService;
import cn.com.emsoft.sw.framework.common.pojo.PageParam;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.pojo.CommonResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;
import static cn.com.emsoft.sw.framework.common.pojo.CommonResult.success;

import cn.com.emsoft.sw.framework.excel.core.util.ExcelUtils;

import cn.com.emsoft.sw.framework.apilog.core.annotation.ApiAccessLog;
import static cn.com.emsoft.sw.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 表务日志")
@RestController
@RequestMapping("/business/meter-log")
@Validated
public class MeterLogController {

    @Resource
    private MeterLogService meterLogService;

    @PostMapping("/create")
    @Operation(summary = "创建表务日志")
    @PreAuthorize("@ss.hasPermission('business:meter-log:create')")
    public CommonResult<Long> createMeterLog(@Valid @RequestBody MeterLogSaveReqVO createReqVO) {
        return success(meterLogService.createMeterLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新表务日志")
    @PreAuthorize("@ss.hasPermission('business:meter-log:update')")
    public CommonResult<Boolean> updateMeterLog(@Valid @RequestBody MeterLogSaveReqVO updateReqVO) {
        meterLogService.updateMeterLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除表务日志")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('business:meter-log:delete')")
    public CommonResult<Boolean> deleteMeterLog(@RequestParam("id") Long id) {
        meterLogService.deleteMeterLog(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除表务日志")
                @PreAuthorize("@ss.hasPermission('business:meter-log:delete')")
    public CommonResult<Boolean> deleteMeterLogList(@RequestParam("ids") List<Long> ids) {
        meterLogService.deleteMeterLogListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得表务日志")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('business:meter-log:query')")
    public CommonResult<MeterLogRespVO> getMeterLog(@RequestParam("id") Long id) {
        MeterLogDO meterLog = meterLogService.getMeterLog(id);
        return success(BeanUtils.toBean(meterLog, MeterLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得表务日志分页")
    @PreAuthorize("@ss.hasPermission('business:meter-log:query')")
    public CommonResult<PageResult<MeterLogRespVO>> getMeterLogPage(@Valid MeterLogPageReqVO pageReqVO) {
        PageResult<MeterLogDO> pageResult = meterLogService.getMeterLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MeterLogRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出表务日志 Excel")
    @PreAuthorize("@ss.hasPermission('business:meter-log:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMeterLogExcel(@Valid MeterLogPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MeterLogDO> list = meterLogService.getMeterLogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "表务日志.xls", "数据", MeterLogRespVO.class,
                        BeanUtils.toBean(list, MeterLogRespVO.class));
    }

}