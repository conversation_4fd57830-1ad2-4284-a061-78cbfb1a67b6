package cn.com.emsoft.sw.business.controller.admin.meter;

import cn.com.emsoft.sw.business.controller.admin.meter.vo.MeterPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meter.vo.MeterRespVO;
import cn.com.emsoft.sw.business.controller.admin.meter.vo.MeterSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meter.MeterDO;
import cn.com.emsoft.sw.business.service.meter.MeterService;
import cn.com.emsoft.sw.framework.common.pojo.PageParam;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.pojo.CommonResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;
import static cn.com.emsoft.sw.framework.common.pojo.CommonResult.success;

import cn.com.emsoft.sw.framework.excel.core.util.ExcelUtils;

import cn.com.emsoft.sw.framework.apilog.core.annotation.ApiAccessLog;
import static cn.com.emsoft.sw.framework.apilog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - 水表信息")
@RestController
@RequestMapping("/business/meter")
@Validated
public class MeterController {

    @Resource
    private MeterService meterService;

    @PostMapping("/create")
    @Operation(summary = "创建水表信息")
    @PreAuthorize("@ss.hasPermission('business:meter:create')")
    public CommonResult<Long> createMeter(@Valid @RequestBody MeterSaveReqVO createReqVO) {
        return success(meterService.createMeter(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新水表信息")
    @PreAuthorize("@ss.hasPermission('business:meter:update')")
    public CommonResult<Boolean> updateMeter(@Valid @RequestBody MeterSaveReqVO updateReqVO) {
        meterService.updateMeter(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除水表信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('business:meter:delete')")
    public CommonResult<Boolean> deleteMeter(@RequestParam("id") Long id) {
        meterService.deleteMeter(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除水表信息")
                @PreAuthorize("@ss.hasPermission('business:meter:delete')")
    public CommonResult<Boolean> deleteMeterList(@RequestParam("ids") List<Long> ids) {
        meterService.deleteMeterListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水表信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('business:meter:query')")
    public CommonResult<MeterRespVO> getMeter(@RequestParam("id") Long id) {
        MeterDO meter = meterService.getMeter(id);
        return success(BeanUtils.toBean(meter, MeterRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得水表信息分页")
    @PreAuthorize("@ss.hasPermission('business:meter:query')")
    public CommonResult<PageResult<MeterRespVO>> getMeterPage(@Valid MeterPageReqVO pageReqVO) {
        PageResult<MeterDO> pageResult = meterService.getMeterPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MeterRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出水表信息 Excel")
    @PreAuthorize("@ss.hasPermission('business:meter:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMeterExcel(@Valid MeterPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MeterDO> list = meterService.getMeterPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "水表信息.xls", "数据", MeterRespVO.class,
                        BeanUtils.toBean(list, MeterRespVO.class));
    }

}