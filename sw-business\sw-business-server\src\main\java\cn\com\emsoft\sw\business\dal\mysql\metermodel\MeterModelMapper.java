package cn.com.emsoft.sw.business.dal.mysql.metermodel;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.metermodel.vo.MeterModelPageReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.metermodel.MeterModelDO;
import cn.com.emsoft.sw.framework.common.enums.CommonStatusEnum;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 水表型号 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MeterModelMapper extends BaseMapperX<MeterModelDO> {

    default PageResult<MeterModelDO> selectPage(MeterModelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MeterModelDO>()
                .likeIfPresent(MeterModelDO::getName, reqVO.getName())
                .likeIfPresent(MeterModelDO::getCode, reqVO.getCode())
                .orderByDesc(MeterModelDO::getId));
    }

    default MeterModelDO selectByCode(String code) {
        return selectOne(MeterModelDO::getCode, code);
    }


    default List<MeterModelDO> selectSimpleList() {
        return selectList(new LambdaQueryWrapperX<MeterModelDO>()
                .eq(MeterModelDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .orderByAsc(MeterModelDO::getName));
    }

    default List<MeterModelDO> selectSimpleListByMakerCode(String makerCode){
        return selectList(new LambdaQueryWrapperX<MeterModelDO>()
                .eq(MeterModelDO::getMakerCode,makerCode)
                .orderByAsc(MeterModelDO::getId));
    }

    default Boolean isMeterMakerRelatedModelExist(String makerCode){
        return this.exists(new LambdaQueryWrapperX<MeterModelDO>()
                .eq(MeterModelDO::getMakerCode,makerCode)
        );
    }

    default void updateRelatedMeterModelMakerCode(String oldMakerCode,String newMakerCode){
        List<MeterModelDO> meterModelDOList = this.selectList(MeterModelDO::getMakerCode,oldMakerCode);
        if (meterModelDOList.isEmpty()){
            return;
        }
        meterModelDOList.forEach(meterModelDO -> meterModelDO.setMakerCode(newMakerCode));
        this.updateBatch(meterModelDOList);
    }

}