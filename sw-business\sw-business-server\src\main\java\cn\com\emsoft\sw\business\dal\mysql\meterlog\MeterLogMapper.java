package cn.com.emsoft.sw.business.dal.mysql.meterlog;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.meterlog.vo.MeterLogPageReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterlog.MeterLogDO;
import cn.com.emsoft.sw.business.dal.dataobject.meterlog.MeterLogDORef;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;

import org.apache.ibatis.annotations.Mapper;


/**
 * 表务日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MeterLogMapper extends BaseMapperX<MeterLogDO> {

    default PageResult<MeterLogDO> selectPage(MeterLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MeterLogDO>()
                // 多字段模糊和排序
                .getSearchAndSort(MeterLogDORef.class, reqVO.getSearch(), reqVO.getSearchContent(), reqVO.getSort())
                .eqIfPresent(MeterLogDO::getMeterId, reqVO.getMeterId())
                .eqIfPresent(MeterLogDO::getMeterInOutId, reqVO.getMeterInOutId())
                .eqIfPresent(MeterLogDO::getType, reqVO.getType())
                .eqIfPresent(MeterLogDO::getResult, reqVO.getResult())
                .orderByDesc(MeterLogDO::getId));
    }

}