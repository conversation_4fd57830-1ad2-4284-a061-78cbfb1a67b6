package cn.com.emsoft.sw.business.controller.admin.meter.vo;

import cn.com.emsoft.sw.framework.common.pojo.SearchAndSortPageParam;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.com.emsoft.sw.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 水表信息分页 Request VO")
@Data
public class MeterPageReqVO extends SearchAndSortPageParam {

    @Schema(description = "入库单号，关联水表出入库单")
    private Long inMeter;

    @Schema(description = "出库单号，关联水表出入库单")
    private Long outMeter;

    @Schema(description = "水表厂家代码，关联水表厂家表")
    private String makerCode;

    @Schema(description = "水表型号代码，关联水表型号表")
    private String modelCode;

    @Schema(description = "水表口径代码，关联水表口径表")
    private Long caliberCode;

    @Schema(description = "水表量程代码，关联水表量程表")
    private Long rangeCode;

    @Schema(description = "水表分类/类型", example = "1")
    private Short type;

    @Schema(description = "钢印号")
    private String steelMark;

    @Schema(description = "水表编号")
    private String sealNumber;

    @Schema(description = "条形码")
    private String barCode;

    @Schema(description = "强检编号")
    private String checkCode;

    @Schema(description = "采集号")
    private String collectCode;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}