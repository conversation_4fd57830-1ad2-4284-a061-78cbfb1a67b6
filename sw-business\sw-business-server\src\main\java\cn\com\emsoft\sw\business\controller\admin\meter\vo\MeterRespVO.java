package cn.com.emsoft.sw.business.controller.admin.meter.vo;

import cn.com.emsoft.sw.business.enums.DictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.com.emsoft.sw.framework.excel.core.annotations.DictFormat;
import cn.com.emsoft.sw.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 水表信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MeterRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16877")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "入库单号，关联水表出入库单")
    @ExcelProperty("入库单号，关联水表出入库单")
    private Long inMeter;

    @Schema(description = "出库单号，关联水表出入库单")
    @ExcelProperty("出库单号，关联水表出入库单")
    private Long outMeter;

    @Schema(description = "水表厂家代码，关联水表厂家表", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("水表厂家代码，关联水表厂家表")
    private String makerCode;

    @Schema(description = "水表型号代码，关联水表型号表")
    @ExcelProperty("水表型号代码，关联水表型号表")
    private String modelCode;

    @Schema(description = "水表口径代码，关联水表口径表", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("水表口径代码，关联水表口径表")
    private Long caliberCode;

    @Schema(description = "水表量程代码，关联水表量程表", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("水表量程代码，关联水表量程表")
    private Long rangeCode;

    @Schema(description = "水表分类/类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "水表分类/类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.METER_OTH_TYPE)
    private Short type;

    @Schema(description = "钢印号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("钢印号")
    private String steelMark;

    @Schema(description = "水表编号")
    @ExcelProperty("水表编号")
    private String sealNumber;

    @Schema(description = "条形码")
    @ExcelProperty("条形码")
    private String barCode;

    @Schema(description = "强检编号")
    @ExcelProperty("强检编号")
    private String checkCode;

    @Schema(description = "生产日期")
    @ExcelProperty("生产日期")
    private LocalDateTime makeDate;

    @Schema(description = "强检日期")
    @ExcelProperty("强检日期")
    private LocalDateTime checkDate;

    @Schema(description = "采集号")
    @ExcelProperty("采集号")
    private String collectCode;

    @Schema(description = "GPSX坐标")
    @ExcelProperty("GPSX坐标")
    private BigDecimal gpsx;

    @Schema(description = "GPSY坐标")
    @ExcelProperty("GPSY坐标")
    private BigDecimal gpsy;

    @Schema(description = "纬度")
    @ExcelProperty("纬度")
    private BigDecimal latitude;

    @Schema(description = "经度")
    @ExcelProperty("经度")
    private BigDecimal longitude;

    @Schema(description = "高度，单位：米")
    @ExcelProperty("高度，单位：米")
    private BigDecimal highly;

    @Schema(description = "设备识别码")
    @ExcelProperty("设备识别码")
    private String imei;

    @Schema(description = "IMSI")
    @ExcelProperty("IMSI")
    private String imsi;

    @Schema(description = "模块号")
    @ExcelProperty("模块号")
    private String moduleCode;

    @Schema(description = "NFC编号")
    @ExcelProperty("NFC编号")
    private String nfcCode;

    @Schema(description = "二维码编号")
    @ExcelProperty("二维码编号")
    private String qrCode;

    @Schema(description = "计量编号")
    @ExcelProperty("计量编号")
    private String measureNo;

    @Schema(description = "水表状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("水表状态")
    @DictFormat(DictTypeConstants.MW_STATE)
    private Short meterStatus;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "状态：0-否，1-是", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态：0-否，1-是")
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Short status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人id")
    @ExcelProperty("创建人id")
    private String creator;

    @Schema(description = "更新人id")
    @ExcelProperty("更新人id")
    private String updater;

    @Schema(description = "是否删除", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否删除")
    private Short deleted;

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "15932")
    @ExcelProperty("租户id")
    private Long tenantId;

}