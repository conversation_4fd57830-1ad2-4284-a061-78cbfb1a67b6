package cn.com.emsoft.sw.business.service.metermodel;

import cn.com.emsoft.sw.business.controller.admin.metermodel.vo.MeterModelCreateReqVO;
import cn.com.emsoft.sw.business.controller.admin.metermodel.vo.MeterModelDetailsRespVO;
import cn.com.emsoft.sw.business.controller.admin.metermodel.vo.MeterModelPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.metermodel.vo.MeterModelUpdateReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.metermaker.MeterMakerDO;
import cn.com.emsoft.sw.business.dal.dataobject.metermodel.MeterModelDO;
import cn.com.emsoft.sw.business.dal.mysql.metermodel.MeterModelMapper;
import cn.com.emsoft.sw.business.service.metermaker.MeterMakerService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;

import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.*;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.com.emsoft.sw.framework.common.util.collection.CollectionUtils.convertList;

/**
 * 水表型号 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MeterModelServiceImpl implements MeterModelService {

    @Resource
    private MeterModelMapper meterModelMapper;

    @Resource
    private MeterMakerService meterMakerService;

    @Override
    public Boolean createMeterModel(MeterModelCreateReqVO createReqVO) {

        // 校验该水表厂家是否存在
        meterMakerService.validateMeterMakerExistsByCode(createReqVO.getMakerCode());

        // 校验水表型号代码唯一性
        validateMeterModelCodeUnique(null, createReqVO.getCode());

        // 插入
        MeterModelDO meterModel = BeanUtils.toBean(createReqVO, MeterModelDO.class);
        meterModelMapper.insert(meterModel);
        // 返回
        Long result = meterModel.getId();
        if (result == null) {
            throw exception(METER_MODEL_CREATE_FAIL);
        }
        return true;
    }

    @Override
    public void updateMeterModel(MeterModelUpdateReqVO updateReqVO) {
        // 校验存在
        validateMeterModelExists(updateReqVO.getId());

        // 校验该水表厂家是否存在
        meterMakerService.validateMeterMakerExistsByCode(updateReqVO.getMakerCode());

        // 校验水表型号代码唯一性
        validateMeterModelCodeUnique(updateReqVO.getId(), updateReqVO.getCode());

        // 更新
        MeterModelDO updateObj = BeanUtils.toBean(updateReqVO, MeterModelDO.class);
        meterModelMapper.updateById(updateObj);
    }

    @Override
    public void deleteMeterModel(Long id) {
        // 校验存在
        validateMeterModelExists(id);
        // 删除
        meterModelMapper.deleteById(id);
    }

    @Override
    public void deleteMeterModelListByIds(List<Long> ids) {
        // 校验存在
        validateMeterModelExists(ids);
        // 删除
        meterModelMapper.deleteByIds(ids);
    }

    private void validateMeterModelExists(List<Long> ids) {
        List<MeterModelDO> list = meterModelMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(METER_MODEL_NOT_EXISTS);
        }
    }

    private void validateMeterModelExists(Long id) {
        if (meterModelMapper.selectById(id) == null) {
            throw exception(METER_MODEL_NOT_EXISTS);
        }
    }

    @Override
    public MeterModelDO getMeterModel(Long id) {
        return meterModelMapper.selectById(id);
    }

    @Override
    public MeterModelDetailsRespVO getMeterModelDetails(Long id) {
        // 校验水表型号是否存在
        MeterModelDO meterModel = meterModelMapper.selectById(id);
        if (meterModel == null) {
            throw exception(METER_MODEL_NOT_EXISTS);
        }

        // 获取厂家信息
        MeterMakerDO meterMaker = meterMakerService.getMeterMakerByCode(meterModel.getMakerCode());

        // 构建返回对象
        MeterModelDetailsRespVO respVO = BeanUtils.toBean(meterModel, MeterModelDetailsRespVO.class);

        // 设置厂家信息
        if (meterMaker != null) {
            MeterModelDetailsRespVO.MakerInfo makerInfo = new MeterModelDetailsRespVO.MakerInfo();
            makerInfo.setId(meterMaker.getId());
            makerInfo.setCode(meterMaker.getCode());
            makerInfo.setName(meterMaker.getName());
            respVO.setMaker(makerInfo);
        }

        return respVO;
    }

    @Override
    public void updateMeterModelStatus(Long id, Integer status) {
        // 校验水表型号存在
        validateMeterModelExists(id);
        // 更新状态
        MeterModelDO updateObj = new MeterModelDO();
        updateObj.setId(id);
        updateObj.setStatus(status.shortValue());
        meterModelMapper.updateById(updateObj);
    }

    @Override
    public PageResult<MeterModelDO> getMeterModelPage(MeterModelPageReqVO pageReqVO) {
        return meterModelMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<MeterModelDetailsRespVO> getMeterModelPageWithMaker(MeterModelPageReqVO pageReqVO) {
        // 获取水表型号分页数据
        PageResult<MeterModelDO> pageResult = meterModelMapper.selectPage(pageReqVO);

        // 转换为包含厂家信息的响应对象
        List<MeterModelDetailsRespVO> list = new ArrayList<>();
        for (MeterModelDO meterModel : pageResult.getList()) {
            MeterModelDetailsRespVO respVO = BeanUtils.toBean(meterModel, MeterModelDetailsRespVO.class);

            // 获取并设置厂家信息
            MeterMakerDO meterMaker = meterMakerService.getMeterMakerByCode(meterModel.getMakerCode());
            if (meterMaker != null) {
                MeterModelDetailsRespVO.MakerInfo makerInfo = new MeterModelDetailsRespVO.MakerInfo();
                makerInfo.setId(meterMaker.getId());
                makerInfo.setCode(meterMaker.getCode());
                makerInfo.setName(meterMaker.getName());
                respVO.setMaker(makerInfo);
            }

            list.add(respVO);
        }

        // 构建分页结果
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public List<MeterModelDO> getSimpleMeterModelList() {
        return meterModelMapper.selectSimpleList();
    }

    @Override
    public List<MeterModelDO> getSimpleMeterModelListByMakerCode(String makerCode) {
        return meterModelMapper.selectSimpleListByMakerCode(makerCode);
    }

    /**
     * 校验水表代码唯一性
     *
     * @param id   水表型号编号，更新时传入，新增时为null
     * @param code 水表型号代码
     */
    private void validateMeterModelCodeUnique(Long id, String code) {
        MeterModelDO meterModelDO = meterModelMapper.selectByCode(code);

        if (meterModelDO == null) {
            return;
        }

        // 存在记录的情况下(id为null说明是新增，第二个是判断更新的时候)
        if (id == null || ObjUtil.notEqual(id, meterModelDO.getId())) {
            throw exception(METER_MODEL_CODE_EXISTS);
        }
    }
}