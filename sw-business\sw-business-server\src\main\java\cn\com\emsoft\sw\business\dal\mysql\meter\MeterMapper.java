package cn.com.emsoft.sw.business.dal.mysql.meter;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.meter.vo.MeterPageReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meter.MeterDO;
import cn.com.emsoft.sw.business.dal.dataobject.meter.MeterDORef;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;

/**
 * 水表信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MeterMapper extends BaseMapperX<MeterDO> {

    default PageResult<MeterDO> selectPage(MeterPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MeterDO>()
                // 多字段模糊和排序
                .getSearchAndSort(MeterDORef.class, reqVO.getSearch(), reqVO.getSearchContent(), reqVO.getSort())
                .eqIfPresent(MeterDO::getInMeter, reqVO.getInMeter())
                .eqIfPresent(MeterDO::getOutMeter, reqVO.getOutMeter())
                .eqIfPresent(MeterDO::getMakerCode, reqVO.getMakerCode())
                .eqIfPresent(MeterDO::getModelCode, reqVO.getModelCode())
                .eqIfPresent(MeterDO::getCaliberCode, reqVO.getCaliberCode())
                .eqIfPresent(MeterDO::getRangeCode, reqVO.getRangeCode())
                .eqIfPresent(MeterDO::getType, reqVO.getType())
                .eqIfPresent(MeterDO::getSteelMark, reqVO.getSteelMark())
                .eqIfPresent(MeterDO::getSealNumber, reqVO.getSealNumber())
                .eqIfPresent(MeterDO::getBarCode, reqVO.getBarCode())
                .eqIfPresent(MeterDO::getCheckCode, reqVO.getCheckCode())
                .eqIfPresent(MeterDO::getCollectCode, reqVO.getCollectCode())
                .betweenIfPresent(MeterDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MeterDO::getId));
    }

}