package cn.com.emsoft.sw.business.controller.admin.meterlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 表务日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MeterLogRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "30946")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水表id，关联水表信息", example = "26430")
    @ExcelProperty("水表id，关联水表信息")
    private Long meterId;

    @Schema(description = "水表出入库id，为出入库时有值", example = "5699")
    @ExcelProperty("水表出入库id，为出入库时有值")
    private Long meterInOutId;

    @Schema(description = "日志类型，关联日志类型", example = "1")
    @ExcelProperty("日志类型，关联日志类型")
    private Short type;

    @Schema(description = "操作结果，成功/失败")
    @ExcelProperty("操作结果，成功/失败")
    private String result;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "状态：0-否，1-是", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("状态：0-否，1-是")
    private Short status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人id")
    @ExcelProperty("创建人id")
    private String creator;

    @Schema(description = "更新人id")
    @ExcelProperty("更新人id")
    private String updater;

    @Schema(description = "是否删除", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否删除")
    private Short deleted;

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20356")
    @ExcelProperty("租户id")
    private Long tenantId;

}