package cn.com.emsoft.sw.business.service.meterinout;

import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.MeterInOutPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.MeterInOutSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinout.MeterInOutDO;
import cn.com.emsoft.sw.business.dal.mysql.meterinout.MeterInOutMapper;
import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;


import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.*;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.com.emsoft.sw.framework.common.util.collection.CollectionUtils.convertList;
import static cn.com.emsoft.sw.framework.common.util.collection.CollectionUtils.diffList;

/**
 * 水表出/入库单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MeterInOutServiceImpl implements MeterInOutService {

    @Resource
    private MeterInOutMapper meterInOutMapper;

    @Override
    public Long createMeterInOut(MeterInOutSaveReqVO createReqVO) {
        // 插入
        MeterInOutDO meterInOut = BeanUtils.toBean(createReqVO, MeterInOutDO.class);
        meterInOutMapper.insert(meterInOut);
        // 返回
        return meterInOut.getId();
    }

    @Override
    public void updateMeterInOut(MeterInOutSaveReqVO updateReqVO) {
        // 校验存在
        validateMeterInOutExists(updateReqVO.getId());
        // 更新
        MeterInOutDO updateObj = BeanUtils.toBean(updateReqVO, MeterInOutDO.class);
        meterInOutMapper.updateById(updateObj);
    }

    @Override
    public void deleteMeterInOut(Long id) {
        // 校验存在
        validateMeterInOutExists(id);
        // 删除
        meterInOutMapper.deleteById(id);
    }

    @Override
        public void deleteMeterInOutListByIds(List<Long> ids) {
        // 校验存在
        validateMeterInOutExists(ids);
        // 删除
        meterInOutMapper.deleteByIds(ids);
        }

    private void validateMeterInOutExists(List<Long> ids) {
        List<MeterInOutDO> list = meterInOutMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(METER_IN_OUT_NOT_EXISTS);
        }
    }

    private void validateMeterInOutExists(Long id) {
        if (meterInOutMapper.selectById(id) == null) {
            throw exception(METER_IN_OUT_NOT_EXISTS);
        }
    }

    @Override
    public MeterInOutDO getMeterInOut(Long id) {
        return meterInOutMapper.selectById(id);
    }

    @Override
    public PageResult<MeterInOutDO> getMeterInOutPage(MeterInOutPageReqVO pageReqVO) {
        return meterInOutMapper.selectPage(pageReqVO);
    }

}