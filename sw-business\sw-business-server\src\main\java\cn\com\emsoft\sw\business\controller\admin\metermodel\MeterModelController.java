package cn.com.emsoft.sw.business.controller.admin.metermodel;

import cn.com.emsoft.sw.business.controller.admin.metermodel.vo.*;
import cn.com.emsoft.sw.business.dal.dataobject.metermodel.MeterModelDO;
import cn.com.emsoft.sw.business.service.metermodel.MeterModelService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.com.emsoft.sw.framework.common.pojo.PageParam;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.pojo.CommonResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;
import static cn.com.emsoft.sw.framework.common.pojo.CommonResult.success;

import cn.com.emsoft.sw.framework.excel.core.util.ExcelUtils;

import cn.com.emsoft.sw.framework.apilog.core.annotation.ApiAccessLog;
import static cn.com.emsoft.sw.framework.apilog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - 水表型号")
@RestController
@RequestMapping("/business/meter-model")
@Validated
public class MeterModelController {

    @Resource
    private MeterModelService meterModelService;

    @PostMapping("/create")
    @Operation(summary = "创建水表型号")
    @PreAuthorize("@ss.hasPermission('business:meter-model:create')")
    public CommonResult<Boolean> createMeterModel(@Valid @RequestBody MeterModelCreateReqVO createReqVO) {
        return success(meterModelService.createMeterModel(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新水表型号")
    @PreAuthorize("@ss.hasPermission('business:meter-model:update')")
    public CommonResult<Boolean> updateMeterModel(@Valid @RequestBody MeterModelUpdateReqVO updateReqVO) {
        meterModelService.updateMeterModel(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除水表型号")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('business:meter-model:delete')")
    public CommonResult<Boolean> deleteMeterModel(@RequestParam("id") Long id) {
        meterModelService.deleteMeterModel(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除水表型号")
    @PreAuthorize("@ss.hasPermission('business:meter-model:delete')")
    public CommonResult<Boolean> deleteMeterModelList(@RequestParam("ids") List<Long> ids) {
        meterModelService.deleteMeterModelListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水表型号")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('business:meter-model:query')")
    public CommonResult<MeterModelDetailsRespVO> getMeterModel(@RequestParam("id") Long id) {
        MeterModelDetailsRespVO meterModel = meterModelService.getMeterModelDetails(id);
        return success(meterModel);
    }

    @PutMapping("/update-status")
    @Operation(summary = "修改水表型号状态")
    @PreAuthorize("@ss.hasPermission('business:meter-model:update')")
    public CommonResult<Boolean> updateMeterMakerStatus(@Valid @RequestBody MeterModelUpdateStatusReqVO reqVO) {
        meterModelService.updateMeterModelStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得水表型号分页")
    @PreAuthorize("@ss.hasPermission('business:meter-model:query')")
    public CommonResult<PageResult<MeterModelDetailsRespVO>> getMeterModelPage(@Valid MeterModelPageReqVO pageReqVO) {
        PageResult<MeterModelDetailsRespVO> pageResult = meterModelService.getMeterModelPageWithMaker(pageReqVO);
        return success(pageResult);
    }

    @GetMapping(value = { "/list-all-simple", "/simple-list" })
    @Operation(summary = "获取水表型号精简信息列表", description = "只包含被启用的水表型号，主要用于前端的下拉选项")
    public CommonResult<List<MeterModelSimpleRespVO>> getSimpleMeterModelList() {
        List<MeterModelDO> list = meterModelService.getSimpleMeterModelList();
        return success(BeanUtils.toBean(list, MeterModelSimpleRespVO.class));
    }

    @GetMapping(value = { "/list-by-maker-code" })
    @Operation(summary = "获取水表型号精简信息列表", description = "只包含被启用的水表型号，主要用于前端的下拉选项")
    public CommonResult<List<MeterModelSimpleRespVO>> getSimpleMeterModelListByMakerCode(@RequestParam("makerCode") String makerCode) {
        List<MeterModelDO> list = meterModelService.getSimpleMeterModelListByMakerCode(makerCode);
        return success(BeanUtils.toBean(list, MeterModelSimpleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出水表型号 Excel")
    @PreAuthorize("@ss.hasPermission('business:meter-model:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMeterModelExcel(@Valid MeterModelPageReqVO pageReqVO,
            HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MeterModelDO> list = meterModelService.getMeterModelPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "水表型号.xls", "数据", MeterModelRespVO.class,
                BeanUtils.toBean(list, MeterModelRespVO.class));
    }

}