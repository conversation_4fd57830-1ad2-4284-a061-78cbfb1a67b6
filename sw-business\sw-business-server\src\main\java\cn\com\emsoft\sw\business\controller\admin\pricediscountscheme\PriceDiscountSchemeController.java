package cn.com.emsoft.sw.business.controller.admin.pricediscountscheme;

import cn.com.emsoft.sw.business.controller.admin.pricediscountscheme.vo.*;
import cn.com.emsoft.sw.business.dal.dataobject.pricediscountscheme.PriceDiscountSchemeDO;
import cn.com.emsoft.sw.business.service.pricediscountscheme.PriceDiscountSchemeService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.com.emsoft.sw.framework.common.pojo.PageParam;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.pojo.CommonResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;
import static cn.com.emsoft.sw.framework.common.pojo.CommonResult.success;

import cn.com.emsoft.sw.framework.excel.core.util.ExcelUtils;

import cn.com.emsoft.sw.framework.apilog.core.annotation.ApiAccessLog;
import static cn.com.emsoft.sw.framework.apilog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - 水价优惠方案")
@RestController
@RequestMapping("/business/price-discount-scheme")
@Validated
public class PriceDiscountSchemeController {

    @Resource
    private PriceDiscountSchemeService priceDiscountSchemeService;

    @PostMapping("/create")
    @Operation(summary = "创建水价优惠方案（包含阶梯和费用详情）")
    @PreAuthorize("@ss.hasPermission('business:price-discount-scheme:create')")
    @ApiAccessLog(enable = false) //暂时解决日志报错问题
    public CommonResult<Long> createPriceDiscountSchemeWithDetails(
            @Valid @RequestBody PriceDiscountSchemeCreateReqVO createReqVO) {
        return success(priceDiscountSchemeService.createPriceDiscountSchemeWithDetails(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新水价优惠方案（包含阶梯和费用详情）")
    @PreAuthorize("@ss.hasPermission('business:price-discount-scheme:update')")
    public CommonResult<Boolean> updatePriceDiscountSchemeWithDetails(
            @Valid @RequestBody PriceDiscountSchemeUpdateReqVO updateReqVO) {
        priceDiscountSchemeService.updatePriceDiscountSchemeWithDetails(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "修改水价优惠方案状态")
    @PreAuthorize("@ss.hasPermission('business:price-discount-scheme:update')")
    public CommonResult<Boolean> updatePriceDiscountSchemeStatus(
            @Valid @RequestBody PriceDiscountSchemeUpdateStatusReqVO reqVO) {
        priceDiscountSchemeService.updatePriceDiscountSchemeStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除水价优惠方案")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('business:price-discount-scheme:delete')")
    public CommonResult<Boolean> deletePriceDiscountScheme(@RequestParam("id") Long id) {
        priceDiscountSchemeService.deletePriceDiscountScheme(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除水价优惠方案")
    @PreAuthorize("@ss.hasPermission('business:price-discount-scheme:delete')")
    public CommonResult<Boolean> deletePriceDiscountSchemeList(@RequestParam("ids") List<Long> ids) {
        priceDiscountSchemeService.deletePriceDiscountSchemeListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水价优惠方案")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('business:price-discount-scheme:query')")
    public CommonResult<PriceDiscountSchemeRespVO> getPriceDiscountScheme(@RequestParam("id") Long id) {
        PriceDiscountSchemeDO priceDiscountScheme = priceDiscountSchemeService.getPriceDiscountScheme(id);
        return success(BeanUtils.toBean(priceDiscountScheme, PriceDiscountSchemeRespVO.class));
    }

    @GetMapping("/get-details")
    @Operation(summary = "获得水价优惠方案详情（包含阶梯和费用详情）")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('business:price-discount-scheme:query')")
    @ApiAccessLog(enable = false) //暂时解决日志报错问题，不影响功能正常执行
    public CommonResult<PriceDiscountSchemeDetailsRespVO> getPriceDiscountSchemeDetails(@RequestParam("id") Long id) {
        PriceDiscountSchemeDetailsRespVO details = priceDiscountSchemeService.getPriceDiscountSchemeDetails(id);
        return success(details);
    }

    @GetMapping(value = { "/list-all-simple", "/simple-list" })
    @Operation(summary = "获取水价优惠方案精简信息列表", description = "水价优惠方案，主要用于前端的下拉选项")
    public CommonResult<List<PriceDiscountSchemeSimpleRespVO>> getSimplePriceDiscountSchemeList() {
        List<PriceDiscountSchemeDO> list = priceDiscountSchemeService.getSimplePriceDiscountSchemeList();
        return success(BeanUtils.toBean(list, PriceDiscountSchemeSimpleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得水价优惠方案分页")
    @PreAuthorize("@ss.hasPermission('business:price-discount-scheme:query')")
    public CommonResult<PageResult<PriceDiscountSchemeRespVO>> getPriceDiscountSchemePage(
            @Valid PriceDiscountSchemePageReqVO pageReqVO) {
        PageResult<PriceDiscountSchemeDO> pageResult = priceDiscountSchemeService.getPriceDiscountSchemePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PriceDiscountSchemeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出水价优惠方案 Excel")
    @PreAuthorize("@ss.hasPermission('business:price-discount-scheme:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPriceDiscountSchemeExcel(@Valid PriceDiscountSchemePageReqVO pageReqVO,
            HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PriceDiscountSchemeDO> list = priceDiscountSchemeService.getPriceDiscountSchemePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "水价优惠方案.xls", "数据", PriceDiscountSchemeRespVO.class,
                BeanUtils.toBean(list, PriceDiscountSchemeRespVO.class));
    }

}