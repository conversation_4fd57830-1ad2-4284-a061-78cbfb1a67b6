package cn.com.emsoft.sw.business.service.metermodel;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.metermodel.vo.MeterModelCreateReqVO;
import cn.com.emsoft.sw.business.controller.admin.metermodel.vo.MeterModelDetailsRespVO;
import cn.com.emsoft.sw.business.controller.admin.metermodel.vo.MeterModelPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.metermodel.vo.MeterModelUpdateReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.metermodel.MeterModelDO;
import jakarta.validation.*;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;

/**
 * 水表型号 Service 接口
 *
 * <AUTHOR>
 */
public interface MeterModelService {

    /**
     * 创建水表型号
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Boolean createMeterModel(@Valid MeterModelCreateReqVO createReqVO);

    /**
     * 更新水表型号
     *
     * @param updateReqVO 更新信息
     */
    void updateMeterModel(@Valid MeterModelUpdateReqVO updateReqVO);

    /**
     * 删除水表型号
     *
     * @param id 编号
     */
    void deleteMeterModel(Long id);

    /**
     * 批量删除水表型号
     *
     * @param ids 编号
     */
    void deleteMeterModelListByIds(List<Long> ids);

    /**
     * 获得水表型号
     *
     * @param id 编号
     * @return 水表型号
     */
    MeterModelDO getMeterModel(Long id);

    /**
     * 获得水表型号详情（包含厂家信息）
     *
     * @param id 编号
     * @return 水表型号详情
     */
    MeterModelDetailsRespVO getMeterModelDetails(Long id);

    /**
     * 修改水表型号状态
     *
     * @param id     水表型号编号
     * @param status 状态
     */
    void updateMeterModelStatus(Long id, Integer status);

    /**
     * 获得水表型号分页
     *
     * @param pageReqVO 分页查询
     * @return 水表型号分页
     */
    PageResult<MeterModelDO> getMeterModelPage(MeterModelPageReqVO pageReqVO);

    /**
     * 获得水表型号分页（包含厂家信息）
     *
     * @param pageReqVO 分页查询
     * @return 水表型号分页（包含厂家信息）
     */
    PageResult<MeterModelDetailsRespVO> getMeterModelPageWithMaker(MeterModelPageReqVO pageReqVO);

    /**
     * 获取水表型号精简信息列表
     *
     * @return 精简的水表型号列表，只包含启用状态的厂家
     */
    List<MeterModelDO> getSimpleMeterModelList();

    /**
     * 通过厂家代码获取水表型号精简信息列表
     *
     * @return 精简的水表型号列表，只包含启用状态的厂家
     */
    List<MeterModelDO> getSimpleMeterModelListByMakerCode(String makerCode);

}