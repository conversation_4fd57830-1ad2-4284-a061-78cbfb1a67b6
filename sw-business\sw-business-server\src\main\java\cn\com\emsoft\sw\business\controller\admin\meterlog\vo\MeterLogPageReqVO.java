package cn.com.emsoft.sw.business.controller.admin.meterlog.vo;

import cn.com.emsoft.sw.framework.common.pojo.SearchAndSortPageParam;
import cn.com.emsoft.sw.framework.common.validation.GetterRef;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.com.emsoft.sw.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 表务日志分页 Request VO")
@Data
@GetterRef
public class MeterLogPageReqVO extends SearchAndSortPageParam {

    @Schema(description = "水表id，关联水表信息", example = "26430")
    private Long meterId;

    @Schema(description = "水表出入库id，为出入库时有值", example = "5699")
    private Long meterInOutId;

    @Schema(description = "日志类型，关联日志类型", example = "1")
    private Short type;

    @Schema(description = "操作结果，成功/失败")
    private String result;

}