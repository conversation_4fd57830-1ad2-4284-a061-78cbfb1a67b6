package cn.com.emsoft.sw.business.enums;

import cn.com.emsoft.sw.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {
    // ========== 水表厂家 1-025-000-000==========
    ErrorCode METER_MAKER_NOT_EXISTS = new ErrorCode(1_025_000_000, "水表厂家不存在");
    ErrorCode METER_MAKER_CODE_EXISTS = new ErrorCode(1_025_000_001, "水表厂家代码已存在");
    ErrorCode METER_MAKER_CREATE_FAIL = new ErrorCode(1_025_000_002, "创建失败，请联系管理员处理");

    ErrorCode METER_MAKER_HAS_RELATED_MODELS = new ErrorCode(1_025_000_003, "删除失败，该水表厂家下还存在水表型号数据");

    // ========== 水表型号 1-025-001-000 ==========
    ErrorCode METER_MODEL_NOT_EXISTS = new ErrorCode(1_025_001_000, "水表型号不存在");

    ErrorCode METER_MODEL_CODE_EXISTS = new ErrorCode(1_025_001_001, "水表口型号代码已存在");
    ErrorCode METER_MODEL_CREATE_FAIL = new ErrorCode(1_025_001_002, "创建失败，请联系管理员处理");

    // ========== 水表口径 1-025-002-000 ==========
    ErrorCode METER_CALIBER_NOT_EXISTS = new ErrorCode(1_025_002_000, "水表口径不存在");

    ErrorCode METER_CALIBER_NAME_AND_VALUE_EXISTS = new ErrorCode(1_025_002_001, "水表口径名称或口径值已存在");

    ErrorCode METER_CALIBER_CREATE_FAIL = new ErrorCode(1_025_002_002, "创建失败，请联系管理员处理");

    ErrorCode METER_CALIBER_COEFFICIENT_WRONG = new ErrorCode(1_025_002_003, "水表口径量高系数需要比量低系数大");

    // ========== 水表量程 1-025-003-000 ==========
    ErrorCode METER_RANGE_NOT_EXISTS = new ErrorCode(1_025_003_000, "水表量程不存在");
    ErrorCode METER_RANGE_CODE_EXISTS = new ErrorCode(1_025_003_001, "水表量程代码已存在");

    ErrorCode METER_RANGE_CREATE_FAIL = new ErrorCode(1_025_003_002, "创建失败，请联系管理员处理");

    // ========== 所属小区 1-025-004-000 ==========
    ErrorCode COMMUNITY_NOT_EXISTS = new ErrorCode(1_025_004_000, "所属小区不存在");
    ErrorCode COMMUNITY_CODE_EXISTS = new ErrorCode(1_025_004_001, "小区代码已经存在");
    ErrorCode COMMUNITY_CREATE_FAIL = new ErrorCode(1_025_004_002, "创建失败，请联系管理员处理");

    ErrorCode COMMUNITY_SELF_PARENT_ERROR = new ErrorCode(1_025_004_003, "不能设置自己为父级小区");
    ErrorCode COMMUNITY_SON_PARENT_ERROR = new ErrorCode(1_025_004_004, "不能设置子孙小区为父级小区");

    ErrorCode COMMUNITY_HAS_RELATED_SON = new ErrorCode(1_025_004_005, "删除失败，该小区还存在关联的子小区");
    ErrorCode DEPT_CODE_UPDATE_COMMUNITY_FAIL = new ErrorCode(1_025_004_006, "更新小区部门代码失败");

    // ========== 水司账户 1_025_005_000 ==========
    ErrorCode COMPANY_ACCOUNT_NOT_EXISTS = new ErrorCode(1_025_005_000, "水司账户不存在");
    ErrorCode COMPANY_ACCOUNT_TAX_NUMBER_EXISTS = new ErrorCode(1_025_005_001, "纳税人识别号已存在");
    ErrorCode COMPANY_ACCOUNT_NAME_EXISTS = new ErrorCode(1_025_005_002,"销售方名称已存在");
    // ========== 水价归属 1_025_006_000 ==========
    ErrorCode PRICE_CATEGORY_NOT_EXISTS = new ErrorCode(1_025_006_000, "水价归属不存在");
    ErrorCode PRICE_CATEGORY_NAME_EXISTS = new ErrorCode(1_025_006_001, "水价归属名称已经存在");
    ErrorCode PRICE_CATEGORY_CODE_EXISTS = new ErrorCode(1_025_006_002, "水价归属代码已经存在");

    ErrorCode PRICE_CATEGORY_SELF_PARENT_ERROR = new ErrorCode(1_025_006_003, "不能设置自己为父级水价归属");
    ErrorCode PRICE_CATEGORY_SON_BE_PARENT_ERROR = new ErrorCode(1_025_006_004, "不能设置子孙水价归属为父级水价归属");

    ErrorCode PRICE_CATEGORY_HAS_RELATED_SON = new ErrorCode(1_025_006_005, "删除失败，该水价归属还存在关联的子水价归属");
    ErrorCode PRICE_CATEGORY_CREATE_FAIL = new ErrorCode(1_025_006_006, "创建失败，请联系管理员处理");

    // ========== 费用组成 1_025_007_000 ==========
    ErrorCode COST_COMPONENT_NOT_EXISTS = new ErrorCode(1_025_007_000, "费用组成不存在");
    ErrorCode COST_COMPONENT_CODE_EXISTS = new ErrorCode(1_025_007_001, "费用组成代码已存在");
    ErrorCode COST_COMPONENT_NAME_EXISTS = new ErrorCode(1_025_007_002, "费用组成名称已存在");
    ErrorCode COST_COMPONENT_CREATE_FAIL = new ErrorCode(1_025_007_003, "创建失败，请联系管理员处理");

    // ========== 计划用水方案 1_025_008_000 ==========
    ErrorCode WATER_USE_SCHEME_NOT_EXISTS = new ErrorCode(1_025_008_000, "计划用水方案不存在");
    ErrorCode WATER_USE_SCHEME_NAME_EXISTS = new ErrorCode(1_025_008_001, "计划用水方案名称已存在");
    ErrorCode WATER_USE_SCHEME_CREATE_FAIL = new ErrorCode(1_025_008_002, "计划用水方案创建失败，请联系管理员处理");

    // ========== 计划用水方案阶梯 1_025_009_000 ==========
    ErrorCode WATER_USE_SCHEME_TIER_NOT_EXISTS = new ErrorCode(1_025_009_000, "计划用水方案阶梯不存在");

    ErrorCode WATER_USE_SCHEME_TIER_CREATE_FAIL = new ErrorCode(1_025_009_001, "计划用水方案阶梯创建失败，请联系管理员处理");
    ErrorCode WATER_USE_SCHEME_TIER_UPDATE_FAIL = new ErrorCode(1_025_009_002, "计划用水方案阶梯更新失败，请联系管理员处理");
    ErrorCode WATER_USE_SCHEME_TIER_DELETE_FAIL = new ErrorCode(1_025_009_003, "计划用水方案阶梯删除失败，请联系管理员处理");

    // ========== 水价调整模版 1_025_010_000 ==========
    ErrorCode PRICE_TEMPLATE_NOT_EXISTS = new ErrorCode(1_025_010_000, "水价调整模版不存在");
    ErrorCode PRICE_TEMPLATE_NAME_EXISTS = new ErrorCode(1_025_010_001, "水价调整模版用水性质已存在");
    ErrorCode PRICE_TEMPLATE_CODE_EXISTS = new ErrorCode(1_025_010_002, "水价调整模版简号已存在");
    ErrorCode PRICE_TEMPLATE_ID_NOT_EXISTS_IN_PREVIOUS_VERSION = new ErrorCode(1_025_010_003, "模板ID在上一版本中不存在");
    ErrorCode DEPT_CODE_UPDATE_TEMPLATE_DEPT_REL_FAIL = new ErrorCode(1_025_010_004, "更新模板部门关系代码失败");

    // ========== 水价调整快照（历史） 1_025_011_000 ==========
    ErrorCode PRICE_ADJUSTMENT_SNAP_NOT_EXISTS = new ErrorCode(1_025_011_000, "水价调整快照（历史）不存在");

    // ========== 水价费用调整 1_025_012_000 ==========
    ErrorCode PRICE_COST_ADJUSTMENT_NOT_EXISTS = new ErrorCode(1_025_012_000, "水价费用调整不存在");

    // ========== 水价阶梯调整 1_025_013_000 ==========
    ErrorCode PRICE_TIER_ADJUSTMENT_NOT_EXISTS = new ErrorCode(1_025_013_000, "水价阶梯调整不存在");

    // ========== 水价调整模板和部门关系 1_025_014_000 ==========
    ErrorCode TEMPLATE_DEPT_REL_NOT_EXISTS = new ErrorCode(1_025_014_000, "水价调整模板和部门关系不存在");
    ErrorCode DEPT_HAS_RELATED_PRICE_TEMPLATES = new ErrorCode(1_025_014_001, "删除失败，该部门还存在关联的水价调整模板");

    // ========== 水价优惠费用 1_025_015_000 ==========
    ErrorCode PRICE_DISCOUNT_COST_NOT_EXISTS = new ErrorCode(1_025_015_000, "水价优惠费用不存在");

    // ========== 水价优惠阶梯 1_025_016_000 ==========
    ErrorCode PRICE_DISCOUNT_TIER_NOT_EXISTS = new ErrorCode(1_025_016_000, "水价优惠阶梯不存在");

    // ========== 水价优惠方案 1_025_017_000 ==========
    ErrorCode PRICE_DISCOUNT_SCHEME_NOT_EXISTS = new ErrorCode(1_025_017_000, "水价优惠方案不存在");
    ErrorCode PRICE_DISCOUNT_SCHEME_NAME_EXISTS = new ErrorCode(1_025_017_001, "水价优惠方案名称已存在");
    ErrorCode PRICE_DISCOUNT_SCHEME_TIER_DATA_EMPTY = new ErrorCode(1_025_017_002, "阶梯数据不能为空");
    ErrorCode PRICE_DISCOUNT_SCHEME_TIER_DATA_DISCONTINUOUS = new ErrorCode(1_025_017_003,
            "阶梯数据不连续，上一个阶梯的结束水量应等于下一个阶梯的开始水量");
    ErrorCode PRICE_DISCOUNT_SCHEME_UPDATE_FAIL = new ErrorCode(1_025_017_004, "水价优惠方案更新失败");

    // ========== 部门和水司账户关系 1_025_018_000 ==========
    ErrorCode DEPT_ACCOUNT_REL_NOT_EXISTS = new ErrorCode(1_025_018_000, "部门和水司账户关系不存在");
    ErrorCode DEPT_ACCOUNT_REL_ACCOUNT_NOT_EXISTS = new ErrorCode(1_025_018_001, "水司账户不存在");
    ErrorCode DEPT_ACCOUNT_REL_DEPT_CODE_EMPTY = new ErrorCode(1_025_018_002, "部门代码列表不能为空");
    ErrorCode DEPT_ACCOUNT_REL_CHANGE_FAIL = new ErrorCode(1_025_018_003, "部门账户关系变更失败");

    // ========== 水表出/入库单 1_025_019_000 ==========
    ErrorCode METER_IN_OUT_NOT_EXISTS = new ErrorCode(1_025_019_000, "水表出/入库单不存在");

    // ========== 水表信息 1_025_020_000 ==========
    ErrorCode METER_NOT_EXISTS = new ErrorCode(1_025_020_000, "水表信息不存在");

    // ========== 表务日志 1_025_021_000 ==========
    ErrorCode METER_LOG_NOT_EXISTS = new ErrorCode(1_025_021_000, "表务日志不存在");
}
