package cn.com.emsoft.sw.business.controller.admin.meterinout.vo;

import cn.com.emsoft.sw.framework.common.pojo.SearchAndSortPageParam;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.com.emsoft.sw.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 水表出/入库单分页 Request VO")
@Data
public class MeterInOutPageReqVO extends SearchAndSortPageParam {

    @Schema(description = "营业站点", example = "14736")
    private Long deptId;

    @Schema(description = "采购单编号")
    private String payCode;

    @Schema(description = "库存单编号，RK/CK + 6位随机")
    private String stockCode;

    @Schema(description = "库存单类型，1=入库，2=出库", example = "1")
    private Short stockType;

    @Schema(description = "出/入库人，记入用户名")
    private String operationUser;

    @Schema(description = "出/入库时间")
    private LocalDateTime operationTime;

}