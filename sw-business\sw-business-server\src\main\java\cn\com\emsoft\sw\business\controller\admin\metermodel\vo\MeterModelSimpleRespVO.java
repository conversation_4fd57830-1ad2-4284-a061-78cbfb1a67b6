package cn.com.emsoft.sw.business.controller.admin.metermodel.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 水表型号精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MeterModelSimpleRespVO {


    @Schema(description = "水表型号编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long id;

    @Schema(description = "水表型号代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx")
    private String code;

    @Schema(description = "水表型号名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx")
    private String name;

    @Schema(description = "厂家代码，关联水表厂家表", requiredMode = Schema.RequiredMode.REQUIRED)
    private String makerCode;

    @Schema(description = "状态：0-是（启用），1-否（禁用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Short status;
}
