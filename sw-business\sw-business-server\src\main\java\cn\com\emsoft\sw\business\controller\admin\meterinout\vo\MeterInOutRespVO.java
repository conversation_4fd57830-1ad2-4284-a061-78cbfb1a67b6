package cn.com.emsoft.sw.business.controller.admin.meterinout.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 水表出/入库单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MeterInOutRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2807")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "营业站点", requiredMode = Schema.RequiredMode.REQUIRED, example = "14736")
    @ExcelProperty("营业站点")
    private Long deptId;

    @Schema(description = "采购单编号")
    @ExcelProperty("采购单编号")
    private String payCode;

    @Schema(description = "库存单编号，RK/CK + 6位随机", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("库存单编号，RK/CK + 6位随机")
    private String stockCode;

    @Schema(description = "库存单类型，1=入库，2=出库", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("库存单类型，1=入库，2=出库")
    private Short stockType;

    @Schema(description = "出/入库人，记入用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出/入库人，记入用户名")
    private String operationUser;

    @Schema(description = "出/入库时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出/入库时间")
    private LocalDateTime operationTime;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "状态：0-否，1-是", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态：0-否，1-是")
    private Short status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人id")
    @ExcelProperty("创建人id")
    private String creator;

    @Schema(description = "更新人id")
    @ExcelProperty("更新人id")
    private String updater;

    @Schema(description = "是否删除", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否删除")
    private Short deleted;

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23450")
    @ExcelProperty("租户id")
    private Long tenantId;

}