package cn.com.emsoft.sw.business.dal.mysql.meterinout;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.MeterInOutPageReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinout.MeterInOutDO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinout.MeterInOutDORef;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;


/**
 * 水表出/入库单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MeterInOutMapper extends BaseMapperX<MeterInOutDO> {

    default PageResult<MeterInOutDO> selectPage(MeterInOutPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MeterInOutDO>()
                // 多字段模糊和排序
                .getSearchAndSort(MeterInOutDORef.class, reqVO.getSearch(), reqVO.getSearchContent(), reqVO.getSort())
                .eqIfPresent(MeterInOutDO::getDeptId, reqVO.getDeptId())
                .likeIfPresent(MeterInOutDO::getPayCode, reqVO.getPayCode())
                .likeIfPresent(MeterInOutDO::getStockCode, reqVO.getStockCode())
                .eqIfPresent(MeterInOutDO::getStockType, reqVO.getStockType())
                .eqIfPresent(MeterInOutDO::getOperationUser, reqVO.getOperationUser())
                .eqIfPresent(MeterInOutDO::getOperationTime, reqVO.getOperationTime())
                .orderByDesc(MeterInOutDO::getId));
    }

}