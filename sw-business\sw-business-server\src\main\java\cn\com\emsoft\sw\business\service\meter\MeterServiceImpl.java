package cn.com.emsoft.sw.business.service.meter;

import cn.com.emsoft.sw.business.controller.admin.meter.vo.MeterPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meter.vo.MeterSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meter.MeterDO;
import cn.com.emsoft.sw.business.dal.mysql.meter.MeterMapper;
import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;

import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;

import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.*;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.com.emsoft.sw.framework.common.util.collection.CollectionUtils.convertList;
import static cn.com.emsoft.sw.framework.common.util.collection.CollectionUtils.diffList;

/**
 * 水表信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MeterServiceImpl implements MeterService {

    @Resource
    private MeterMapper meterMapper;

    @Override
    public Long createMeter(MeterSaveReqVO createReqVO) {
        // 插入
        MeterDO meter = BeanUtils.toBean(createReqVO, MeterDO.class);
        meterMapper.insert(meter);
        // 返回
        return meter.getId();
    }

    @Override
    public void updateMeter(MeterSaveReqVO updateReqVO) {
        // 校验存在
        validateMeterExists(updateReqVO.getId());
        // 更新
        MeterDO updateObj = BeanUtils.toBean(updateReqVO, MeterDO.class);
        meterMapper.updateById(updateObj);
    }

    @Override
    public void deleteMeter(Long id) {
        // 校验存在
        validateMeterExists(id);
        // 删除
        meterMapper.deleteById(id);
    }

    @Override
        public void deleteMeterListByIds(List<Long> ids) {
        // 校验存在
        validateMeterExists(ids);
        // 删除
        meterMapper.deleteByIds(ids);
        }

    private void validateMeterExists(List<Long> ids) {
        List<MeterDO> list = meterMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(METER_NOT_EXISTS);
        }
    }

    private void validateMeterExists(Long id) {
        if (meterMapper.selectById(id) == null) {
            throw exception(METER_NOT_EXISTS);
        }
    }

    @Override
    public MeterDO getMeter(Long id) {
        return meterMapper.selectById(id);
    }

    @Override
    public PageResult<MeterDO> getMeterPage(MeterPageReqVO pageReqVO) {
        return meterMapper.selectPage(pageReqVO);
    }

}