package cn.com.emsoft.sw.business.service.meterlog;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.meterlog.vo.MeterLogPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meterlog.vo.MeterLogSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterlog.MeterLogDO;
import jakarta.validation.*;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;


/**
 * 表务日志 Service 接口
 *
 * <AUTHOR>
 */
public interface MeterLogService {

    /**
     * 创建表务日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMeterLog(@Valid MeterLogSaveReqVO createReqVO);

    /**
     * 更新表务日志
     *
     * @param updateReqVO 更新信息
     */
    void updateMeterLog(@Valid MeterLogSaveReqVO updateReqVO);

    /**
     * 删除表务日志
     *
     * @param id 编号
     */
    void deleteMeterLog(Long id);

    /**
    * 批量删除表务日志
    *
    * @param ids 编号
    */
    void deleteMeterLogListByIds(List<Long> ids);

    /**
     * 获得表务日志
     *
     * @param id 编号
     * @return 表务日志
     */
    MeterLogDO getMeterLog(Long id);

    /**
     * 获得表务日志分页
     *
     * @param pageReqVO 分页查询
     * @return 表务日志分页
     */
    PageResult<MeterLogDO> getMeterLogPage(MeterLogPageReqVO pageReqVO);

}