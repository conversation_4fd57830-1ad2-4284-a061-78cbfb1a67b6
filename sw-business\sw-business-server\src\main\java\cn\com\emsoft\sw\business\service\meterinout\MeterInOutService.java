package cn.com.emsoft.sw.business.service.meterinout;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.MeterInOutPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.MeterInOutSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinout.MeterInOutDO;
import jakarta.validation.*;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;

/**
 * 水表出/入库单 Service 接口
 *
 * <AUTHOR>
 */
public interface MeterInOutService {

    /**
     * 创建水表出/入库单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMeterInOut(@Valid MeterInOutSaveReqVO createReqVO);

    /**
     * 更新水表出/入库单
     *
     * @param updateReqVO 更新信息
     */
    void updateMeterInOut(@Valid MeterInOutSaveReqVO updateReqVO);

    /**
     * 删除水表出/入库单
     *
     * @param id 编号
     */
    void deleteMeterInOut(Long id);

    /**
    * 批量删除水表出/入库单
    *
    * @param ids 编号
    */
    void deleteMeterInOutListByIds(List<Long> ids);

    /**
     * 获得水表出/入库单
     *
     * @param id 编号
     * @return 水表出/入库单
     */
    MeterInOutDO getMeterInOut(Long id);

    /**
     * 获得水表出/入库单分页
     *
     * @param pageReqVO 分页查询
     * @return 水表出/入库单分页
     */
    PageResult<MeterInOutDO> getMeterInOutPage(MeterInOutPageReqVO pageReqVO);

}