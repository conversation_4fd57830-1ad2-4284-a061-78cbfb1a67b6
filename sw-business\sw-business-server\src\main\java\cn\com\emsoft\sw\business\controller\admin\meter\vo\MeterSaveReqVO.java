package cn.com.emsoft.sw.business.controller.admin.meter.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 水表信息新增/修改 Request VO")
@Data
public class MeterSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16877")
    private Long id;

    @Schema(description = "入库单号，关联水表出入库单")
    private Long inMeter;

    @Schema(description = "出库单号，关联水表出入库单")
    private Long outMeter;

    @Schema(description = "水表厂家代码，关联水表厂家表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "水表厂家代码，关联水表厂家表不能为空")
    private String makerCode;

    @Schema(description = "水表型号代码，关联水表型号表")
    private String modelCode;

    @Schema(description = "水表口径代码，关联水表口径表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "水表口径代码，关联水表口径表不能为空")
    private Long caliberCode;

    @Schema(description = "水表量程代码，关联水表量程表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "水表量程代码，关联水表量程表不能为空")
    private Long rangeCode;

    @Schema(description = "水表分类/类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "水表分类/类型不能为空")
    private Short type;

    @Schema(description = "钢印号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "钢印号不能为空")
    private String steelMark;

    @Schema(description = "水表编号")
    private String sealNumber;

    @Schema(description = "条形码")
    private String barCode;

    @Schema(description = "强检编号")
    private String checkCode;

    @Schema(description = "生产日期")
    private LocalDateTime makeDate;

    @Schema(description = "强检日期")
    private LocalDateTime checkDate;

    @Schema(description = "采集号")
    private String collectCode;

    @Schema(description = "GPSX坐标")
    private BigDecimal gpsx;

    @Schema(description = "GPSY坐标")
    private BigDecimal gpsy;

    @Schema(description = "纬度")
    private BigDecimal latitude;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "高度，单位：米")
    private BigDecimal highly;

    @Schema(description = "设备识别码")
    private String imei;

    @Schema(description = "IMSI")
    private String imsi;

    @Schema(description = "模块号")
    private String moduleCode;

    @Schema(description = "NFC编号")
    private String nfcCode;

    @Schema(description = "二维码编号")
    private String qrCode;

    @Schema(description = "计量编号")
    private String measureNo;

    @Schema(description = "水表状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "水表状态不能为空")
    private Short meterStatus;

}