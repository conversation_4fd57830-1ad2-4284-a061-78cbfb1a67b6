package cn.com.emsoft.sw.business.controller.admin.meterinout;

import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.MeterInOutPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.MeterInOutRespVO;
import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.MeterInOutSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinout.MeterInOutDO;
import cn.com.emsoft.sw.business.service.meterinout.MeterInOutService;
import cn.com.emsoft.sw.framework.common.pojo.PageParam;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.pojo.CommonResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;
import static cn.com.emsoft.sw.framework.common.pojo.CommonResult.success;

import cn.com.emsoft.sw.framework.excel.core.util.ExcelUtils;

import cn.com.emsoft.sw.framework.apilog.core.annotation.ApiAccessLog;
import static cn.com.emsoft.sw.framework.apilog.core.enums.OperateTypeEnum.*;

@Tag(name = "管理后台 - 水表出/入库单")
@RestController
@RequestMapping("/business/meter-in-out")
@Validated
public class MeterInOutController {

    @Resource
    private MeterInOutService meterInOutService;

    @PostMapping("/create")
    @Operation(summary = "创建水表出/入库单")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:create')")
    public CommonResult<Long> createMeterInOut(@Valid @RequestBody MeterInOutSaveReqVO createReqVO) {
        return success(meterInOutService.createMeterInOut(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新水表出/入库单")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:update')")
    public CommonResult<Boolean> updateMeterInOut(@Valid @RequestBody MeterInOutSaveReqVO updateReqVO) {
        meterInOutService.updateMeterInOut(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除水表出/入库单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:delete')")
    public CommonResult<Boolean> deleteMeterInOut(@RequestParam("id") Long id) {
        meterInOutService.deleteMeterInOut(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除水表出/入库单")
                @PreAuthorize("@ss.hasPermission('business:meter-in-out:delete')")
    public CommonResult<Boolean> deleteMeterInOutList(@RequestParam("ids") List<Long> ids) {
        meterInOutService.deleteMeterInOutListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水表出/入库单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:query')")
    public CommonResult<MeterInOutRespVO> getMeterInOut(@RequestParam("id") Long id) {
        MeterInOutDO meterInOut = meterInOutService.getMeterInOut(id);
        return success(BeanUtils.toBean(meterInOut, MeterInOutRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得水表出/入库单分页")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:query')")
    public CommonResult<PageResult<MeterInOutRespVO>> getMeterInOutPage(@Valid MeterInOutPageReqVO pageReqVO) {
        PageResult<MeterInOutDO> pageResult = meterInOutService.getMeterInOutPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MeterInOutRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出水表出/入库单 Excel")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMeterInOutExcel(@Valid MeterInOutPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MeterInOutDO> list = meterInOutService.getMeterInOutPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "水表出/入库单.xls", "数据", MeterInOutRespVO.class,
                        BeanUtils.toBean(list, MeterInOutRespVO.class));
    }

}